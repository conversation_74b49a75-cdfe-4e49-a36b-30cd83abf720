{"name": "lavive-deployment", "version": "1.0.0", "description": "Lavive Delivery Management System", "main": "backend/server.js", "scripts": {"start": "node backend/server.js", "build": "echo 'Build complete'", "dev": "node backend/server.js"}, "engines": {"node": ">=16.0.0"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.8.1", "dotenv": "^16.3.1", "firebase-admin": "^11.10.1", "jsonwebtoken": "^9.0.1", "bcryptjs": "^2.4.3"}}