<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Commed Delivery Management</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Loading Spinner -->
    <div id="loading-spinner" class="loading-spinner">
        <div class="spinner"></div>
        <p>Loading...</p>
    </div>

    <!-- Login Screen -->
    <div id="login-screen" class="screen">
        <div class="login-container">
            <div class="login-header">
                <h1><i class="fas fa-truck"></i> Commed Delivery</h1>
                <p>Management System</p>
            </div>
            
            <form id="login-form" class="login-form">
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i> Sign In
                </button>
                
                <div id="login-error" class="error-message"></div>
            </form>
        </div>
    </div>

    <!-- Main Application -->
    <div id="main-app" class="screen">
        <!-- Navigation Header -->
        <header class="app-header">
            <div class="header-left">
                <h1><i class="fas fa-truck"></i> Commed Delivery</h1>
            </div>
            <div class="header-right">
                <span id="user-name" class="user-name"></span>
                <button id="logout-btn" class="btn btn-secondary">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard View -->
            <div id="dashboard-view" class="view active">
                <div class="view-header">
                    <h2><i class="fas fa-tachometer-alt"></i> Dashboard</h2>
                    <button id="new-order-btn" class="btn btn-primary">
                        <i class="fas fa-plus"></i> New Order
                    </button>
                </div>

                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-orders">0</h3>
                            <p>Total Orders</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="pending-orders">0</h3>
                            <p>Pending</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="shipped-orders">0</h3>
                            <p>Shipped</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="delivered-orders">0</h3>
                            <p>Delivered</p>
                        </div>
                    </div>
                </div>

                <!-- Orders Table -->
                <div class="table-container">
                    <div class="table-header">
                        <h3>Recent Orders</h3>
                        <div class="table-controls">
                            <input type="text" id="search-orders" placeholder="Search orders..." class="search-input">
                            <select id="filter-status" class="filter-select">
                                <option value="">All Status</option>
                                <option value="New">New</option>
                                <option value="Processing">Processing</option>
                                <option value="Shipped">Shipped</option>
                                <option value="Delivered">Delivered</option>
                                <option value="Cancelled">Cancelled</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="table-wrapper">
                        <table id="orders-table" class="orders-table">
                            <thead>
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Phone</th>
                                    <th>Article</th>
                                    <th>Quantity</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="orders-tbody">
                                <!-- Orders will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Order Form View -->
            <div id="order-form-view" class="view">
                <div class="view-header">
                    <h2 id="form-title"><i class="fas fa-plus"></i> New Order</h2>
                    <button id="back-to-dashboard" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </button>
                </div>

                <form id="order-form" class="order-form">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="customer-name">Customer Name *</label>
                            <input type="text" id="customer-name" name="customerName" required>
                        </div>

                        <div class="form-group">
                            <label for="customer-phone">Phone Number *</label>
                            <input type="tel" id="customer-phone" name="customerPhone" required>
                        </div>

                        <div class="form-group full-width">
                            <label for="delivery-address">Delivery Address *</label>
                            <textarea id="delivery-address" name="deliveryAddress" rows="3" required></textarea>
                        </div>

                        <div class="form-group">
                            <label for="article-model">Article Model *</label>
                            <input type="text" id="article-model" name="articleModel" required>
                        </div>

                        <div class="form-group">
                            <label for="quantity">Quantity *</label>
                            <input type="number" id="quantity" name="quantity" min="1" required>
                        </div>

                        <div class="form-group">
                            <label for="status">Status</label>
                            <select id="status" name="status">
                                <option value="New">New</option>
                                <option value="Processing">Processing</option>
                                <option value="Shipped">Shipped</option>
                                <option value="Delivered">Delivered</option>
                                <option value="Cancelled">Cancelled</option>
                            </select>
                        </div>

                        <div class="form-group full-width">
                            <label for="notes">Notes</label>
                            <textarea id="notes" name="notes" rows="3" placeholder="Special instructions or additional information..."></textarea>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" id="cancel-form" class="btn btn-secondary">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Order
                        </button>
                    </div>
                </form>
            </div>

            <!-- Order Detail View -->
            <div id="order-detail-view" class="view">
                <div class="view-header">
                    <h2 id="detail-title"><i class="fas fa-file-alt"></i> Order Details</h2>
                    <div class="header-actions">
                        <button id="edit-order-btn" class="btn btn-primary">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button id="back-from-detail" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back
                        </button>
                    </div>
                </div>

                <div id="order-details" class="order-details">
                    <!-- Order details will be populated here -->
                </div>
            </div>
        </main>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-auth-compat.js"></script>
    
    <!-- Application Scripts -->
    <script src="js/config.js?v=20250921"></script>
    <script src="js/auth.js?v=20250921"></script>
    <script src="js/api.js?v=20250921"></script>
    <script src="js/ui.js?v=20250921"></script>
    <script src="js/app.js?v=20250921"></script>
</body>
</html>
