// Authentication module for Firebase Auth integration

class AuthManager {
    constructor() {
        this.currentUser = null;
        this.authToken = null;
        this.initializeFirebase();
    }

    // Initialize Firebase
    initializeFirebase() {
        try {
            firebase.initializeApp(CONFIG.FIREBASE);
            this.auth = firebase.auth();
            
            // Set up auth state listener
            this.auth.onAuthStateChanged((user) => {
                this.handleAuthStateChange(user);
            });
            
            console.log('Firebase initialized successfully');
        } catch (error) {
            console.error('Firebase initialization failed:', error);
            UI.showToast('Firebase initialization failed', 'error');
        }
    }

    // Handle authentication state changes
    async handleAuthStateChange(user) {
        if (user) {
            try {
                // Get the ID token
                this.authToken = await user.getIdToken();
                this.currentUser = {
                    uid: user.uid,
                    email: user.email,
                    displayName: user.displayName || user.email
                };

                // Store user data
                localStorage.setItem(CONFIG.STORAGE.USER_TOKEN, this.authToken);
                localStorage.setItem(CONFIG.STORAGE.USER_DATA, JSON.stringify(this.currentUser));

                // Show main app
                this.showMainApp();
            } catch (error) {
                console.error('Error getting user token:', error);
                this.handleAuthError(error);
            }
        } else {
            // User is signed out
            this.currentUser = null;
            this.authToken = null;
            localStorage.removeItem(CONFIG.STORAGE.USER_TOKEN);
            localStorage.removeItem(CONFIG.STORAGE.USER_DATA);
            this.showLoginScreen();
        }
    }

    // Sign in with email and password
    async signIn(email, password) {
        try {
            UI.showLoading();
            
            const userCredential = await this.auth.signInWithEmailAndPassword(email, password);
            
            UI.showToast('Successfully signed in!', 'success');
            return userCredential.user;
        } catch (error) {
            console.error('Sign in error:', error);
            this.handleAuthError(error);
            throw error;
        } finally {
            UI.hideLoading();
        }
    }

    // Sign out
    async signOut() {
        try {
            await this.auth.signOut();
            UI.showToast('Successfully signed out', 'info');
        } catch (error) {
            console.error('Sign out error:', error);
            UI.showToast('Error signing out', 'error');
        }
    }

    // Get current auth token
    async getAuthToken() {
        if (this.currentUser && this.auth.currentUser) {
            try {
                // Refresh token if needed
                this.authToken = await this.auth.currentUser.getIdToken(true);
                localStorage.setItem(CONFIG.STORAGE.USER_TOKEN, this.authToken);
                return this.authToken;
            } catch (error) {
                console.error('Error refreshing token:', error);
                return null;
            }
        }
        return null;
    }

    // Check if user is authenticated
    isAuthenticated() {
        return this.currentUser !== null && this.authToken !== null;
    }

    // Get current user
    getCurrentUser() {
        return this.currentUser;
    }

    // Handle authentication errors
    handleAuthError(error) {
        let message = 'Authentication error';
        
        switch (error.code) {
            case 'auth/user-not-found':
                message = 'No user found with this email';
                break;
            case 'auth/wrong-password':
                message = 'Incorrect password';
                break;
            case 'auth/invalid-email':
                message = 'Invalid email address';
                break;
            case 'auth/user-disabled':
                message = 'This account has been disabled';
                break;
            case 'auth/too-many-requests':
                message = 'Too many failed attempts. Please try again later';
                break;
            case 'auth/network-request-failed':
                message = 'Network error. Please check your connection';
                break;
            default:
                message = error.message || 'Authentication failed';
        }
        
        UI.showToast(message, 'error');
        UI.showError('login-error', message);
    }

    // Show login screen
    showLoginScreen() {
        document.getElementById('login-screen').classList.add('active');
        document.getElementById('main-app').classList.remove('active');
    }

    // Show main application
    showMainApp() {
        document.getElementById('login-screen').classList.remove('active');
        document.getElementById('main-app').classList.add('active');
        
        // Update user name in header
        const userNameElement = document.getElementById('user-name');
        if (userNameElement && this.currentUser) {
            userNameElement.textContent = this.currentUser.displayName;
        }

        // Initialize the dashboard
        if (window.App && window.App.initializeDashboard) {
            window.App.initializeDashboard();
        }
    }

    // Initialize auth from stored data (for page refresh)
    initializeFromStorage() {
        const storedToken = localStorage.getItem(CONFIG.STORAGE.USER_TOKEN);
        const storedUser = localStorage.getItem(CONFIG.STORAGE.USER_DATA);
        
        if (storedToken && storedUser) {
            try {
                this.authToken = storedToken;
                this.currentUser = JSON.parse(storedUser);
                
                // Verify token is still valid by making a test API call
                this.verifyStoredAuth();
            } catch (error) {
                console.error('Error initializing from storage:', error);
                this.clearStoredAuth();
            }
        }
    }

    // Verify stored authentication
    async verifyStoredAuth() {
        try {
            const response = await API.verifyAuth();
            if (response.success) {
                this.showMainApp();
            } else {
                this.clearStoredAuth();
            }
        } catch (error) {
            console.error('Auth verification failed:', error);
            this.clearStoredAuth();
        }
    }

    // Clear stored authentication data
    clearStoredAuth() {
        this.currentUser = null;
        this.authToken = null;
        localStorage.removeItem(CONFIG.STORAGE.USER_TOKEN);
        localStorage.removeItem(CONFIG.STORAGE.USER_DATA);
        this.showLoginScreen();
    }
}

// Create global auth manager instance
window.Auth = new AuthManager();
