const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => console.log(`Server running on port ${PORT}`));
// Security middleware
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// CORS configuration
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:8080',
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Import routes
const orderRoutes = require('./routes/orders');
const authRoutes = require('./routes/auth');

// Use routes
app.use('/api/orders', orderRoutes);
app.use('/api/auth', authRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  const firebaseConfigured = process.env.FIREBASE_PROJECT_ID &&
    !process.env.FIREBASE_PROJECT_ID.includes('placeholder');

  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    service: 'Commed Delivery API',
    firebase_configured: firebaseConfigured,
    environment: process.env.NODE_ENV || 'development'
  });
});

// Development info endpoint (only in development)
if (process.env.NODE_ENV !== 'production') {
  app.get('/dev-info', (req, res) => {
    res.status(200).json({
      message: 'Commed Delivery API - Development Mode',
      endpoints: {
        health: 'GET /health',
        auth_verify: 'POST /api/auth/verify (requires Firebase setup)',
        orders: 'GET /api/orders (requires Firebase setup)',
        create_order: 'POST /api/orders (requires Firebase setup)'
      },
      firebase_status: process.env.FIREBASE_PROJECT_ID &&
        !process.env.FIREBASE_PROJECT_ID.includes('placeholder') ? 'configured' : 'not configured',
      setup_guide: 'See docs/SETUP.md for Firebase configuration'
    });
  });
}

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ 
    error: 'Route not found',
    path: req.originalUrl 
  });
});

// Global error handler
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(err.status || 500).json({
    error: process.env.NODE_ENV === 'production' 
      ? 'Internal server error' 
      : err.message
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Commed Delivery API server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
});

module.exports = app;
