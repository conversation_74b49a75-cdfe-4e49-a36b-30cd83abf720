// Configuration file for the Commed Delivery Management System

// API Configuration
const API_CONFIG = {
    BASE_URL: window.location.hostname === 'localhost'
        ? 'http://localhost:3000/api'
        : 'https://your-backend-url.railway.app/api', // Update this with your actual backend URL
    TIMEOUT: 10000, // 10 seconds
    RETRY_ATTEMPTS: 3
};

// Firebase Configuration
const FIREBASE_CONFIG = {
    apiKey: "AIzaSyB0qZ0y0ZZKcv71SOa3RMUuEmvmxBec5tA",
    authDomain: "lavive-cb219.firebaseapp.com",
    projectId: "lavive-cb219",
    storageBucket: "lavive-cb219.firebasestorage.app",
    messagingSenderId: "1030021729089",
    appId: "1:1030021729089:web:1ccd138ad09c50253aafc0",
    measurementId: "G-0JF0MK7WCX"
};

// Application Configuration
const APP_CONFIG = {
    APP_NAME: 'Commed Delivery Management',
    VERSION: '1.0.0',
    PAGINATION: {
        DEFAULT_PAGE_SIZE: 20,
        MAX_PAGE_SIZE: 100
    },
    TOAST_DURATION: 5000, // 5 seconds
    AUTO_REFRESH_INTERVAL: 30000, // 30 seconds
    ORDER_STATUSES: [
        'New',
        'Processing',
        'Shipped',
        'Delivered',
        'Cancelled'
    ]
};

// Local Storage Keys
const STORAGE_KEYS = {
    USER_TOKEN: 'commed_user_token',
    USER_DATA: 'commed_user_data',
    LAST_REFRESH: 'commed_last_refresh',
    PREFERENCES: 'commed_preferences'
};

// Export configuration for use in other modules
window.CONFIG = {
    API: API_CONFIG,
    FIREBASE: FIREBASE_CONFIG,
    APP: APP_CONFIG,
    STORAGE: STORAGE_KEYS
};
