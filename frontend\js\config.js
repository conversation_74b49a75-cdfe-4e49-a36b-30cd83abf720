// Configuration file for the Commed Delivery Management System
// Updated: 2025-09-21 - Force cache refresh v2
console.log('🚀 CONFIG FILE LOADED - Version 2');

// API Configuration
const API_CONFIG = {
    BASE_URL: (() => {
        const hostname = window.location.hostname;
        console.log('🔧 CONFIG: Current hostname:', hostname);
        console.log('🔧 CONFIG: Location:', window.location.href);

        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            console.log('🔧 CONFIG: Using local backend');
            return 'http://localhost:3000/api';
        } else {
            console.log('🔧 CONFIG: Using production backend');
            const productionURL = 'https://lavive-backend.onrender.com/api';
            console.log('🔧 CONFIG: Production URL:', productionURL);
            return productionURL;
        }
    })(),
    TIMEOUT: 10000, // 10 seconds
    RETRY_ATTEMPTS: 3
};

// Log the final configuration
console.log('🔧 CONFIG: Final API_CONFIG:', API_CONFIG);

// Firebase Configuration
const FIREBASE_CONFIG = {
    apiKey: "AIzaSyB0qZ0y0ZZKcv71SOa3RMUuEmvmxBec5tA",
    authDomain: "lavive-cb219.firebaseapp.com",
    projectId: "lavive-cb219",
    storageBucket: "lavive-cb219.firebasestorage.app",
    messagingSenderId: "1030021729089",
    appId: "1:1030021729089:web:1ccd138ad09c50253aafc0",
    measurementId: "G-0JF0MK7WCX"
};

// Application Configuration
const APP_CONFIG = {
    APP_NAME: 'Commed Delivery Management',
    VERSION: '1.0.0',
    PAGINATION: {
        DEFAULT_PAGE_SIZE: 20,
        MAX_PAGE_SIZE: 100
    },
    TOAST_DURATION: 5000, // 5 seconds
    AUTO_REFRESH_INTERVAL: 30000, // 30 seconds
    ORDER_STATUSES: [
        'New',
        'Processing',
        'Shipped',
        'Delivered',
        'Cancelled'
    ]
};

// Local Storage Keys
const STORAGE_KEYS = {
    USER_TOKEN: 'commed_user_token',
    USER_DATA: 'commed_user_data',
    LAST_REFRESH: 'commed_last_refresh',
    PREFERENCES: 'commed_preferences'
};

// Export configuration for use in other modules
window.CONFIG = {
    API: API_CONFIG,
    FIREBASE: FIREBASE_CONFIG,
    APP: APP_CONFIG,
    STORAGE: STORAGE_KEYS
};
