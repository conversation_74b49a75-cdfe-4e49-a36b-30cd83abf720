{"$schema": "https://railway.com/railway.schema.json", "build": {"builder": "RAILPACK", "buildCommand": "npm install", "watchPatterns": ["/backend"]}, "deploy": {"runtime": "V2", "numReplicas": 1, "startCommand": "npm start", "sleepApplication": false, "useLegacyStacker": false, "multiRegionConfig": {"europe-west4-drams3a": {"numReplicas": 1}}, "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 10}}