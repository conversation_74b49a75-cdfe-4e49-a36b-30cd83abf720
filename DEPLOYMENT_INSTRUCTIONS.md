# Quick Backend Deployment Guide

## Option 1: Railway (Recommended)

### Step 1: Prepare Files
1. Zip the `backend` folder (exclude `node_modules`)
2. Include these files:
   - package.json
   - server.js
   - All folders (config, models, routes, middleware)
   - Procfile
   - railway.json

### Step 2: Deploy to Railway
1. Go to https://railway.app
2. Sign up with GitHub or email
3. Create "New Project" → "Empty Project"
4. Add service → "GitHub Repo" or "Empty Service"
5. Upload your backend zip file
6. Set environment variables (see .env.production)

### Step 3: Get Your Backend URL
After deployment, Railway will give you a URL like:
`https://your-app-name.railway.app`

### Step 4: Update Frontend
1. Edit `frontend/js/config.js`
2. Replace the backend URL:
```javascript
BASE_URL: 'https://your-app-name.railway.app/api'
```
3. Redeploy frontend: `firebase deploy --only hosting`

## Option 2: Render

### Step 1: Deploy to Render
1. Go to https://render.com
2. Sign up and create "Web Service"
3. Choose "Build and deploy from Git" or upload files
4. Configure:
   - Build Command: `npm install`
   - Start Command: `npm start`
   - Environment: Node

### Step 2: Set Environment Variables
Copy all variables from `.env.production` to Render's environment variables section.

## Option 3: Heroku (If you have Git)

```bash
# Install Heroku CLI first
cd backend
heroku create commed-delivery-backend
heroku config:set NODE_ENV=production
heroku config:set FRONTEND_URL=https://lavive-cb219.web.app
# ... set all other environment variables
git add .
git commit -m "Deploy to Heroku"
git push heroku main
```

## Environment Variables to Set

Copy these to your hosting platform:

```
NODE_ENV=production
PORT=3000
FRONTEND_URL=https://lavive-cb219.web.app
FIREBASE_PROJECT_ID=lavive-cb219
FIREBASE_PRIVATE_KEY_ID=19b7582ae25b053da5b33b83288ad86daa9d4244
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n[your-full-private-key]\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=100489032623938359419
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40lavive-cb219.iam.gserviceaccount.com
JWT_SECRET=your-super-secret-jwt-key-here-change-this-in-production
```

## After Backend Deployment

1. Get your backend URL (e.g., `https://your-app.railway.app`)
2. Update `frontend/js/config.js` with the new URL
3. Redeploy frontend: `firebase deploy --only hosting`
4. Test your application at https://lavive-cb219.web.app

## Troubleshooting

- If backend fails to start, check the logs in your hosting platform
- Ensure all environment variables are set correctly
- Make sure the FRONTEND_URL matches your Firebase Hosting URL
- Check that Firestore security rules allow your backend service account
