services:
  - type: web
    name: commed-delivery-backend
    env: node
    region: oregon
    plan: free
    buildCommand: cd backend && npm install
    startCommand: cd backend && npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: FRONTEND_URL
        value: https://lavive-cb219.web.app
      - key: FIREBASE_PROJECT_ID
        value: lavive-cb219
      - key: FIREBASE_PRIVATE_KEY_ID
        value: 19b7582ae25b053da5b33b83288ad86daa9d4244
      - key: FIREBASE_CLIENT_EMAIL
        value: <EMAIL>
      - key: FIREBASE_CLIENT_ID
        value: 100489032623938359419
      - key: FIREBASE_AUTH_URI
        value: https://accounts.google.com/o/oauth2/auth
      - key: FIREBASE_TOKEN_URI
        value: https://oauth2.googleapis.com/token
      - key: FIREBASE_AUTH_PROVIDER_X509_CERT_URL
        value: https://www.googleapis.com/oauth2/v1/certs
      - key: FIREBASE_CLIENT_X509_CERT_URL
        value: https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40lavive-cb219.iam.gserviceaccount.com
      - key: JWT_SECRET
        value: your-super-secret-jwt-key-change-this-in-production-12345
      - key: FIREBASE_PRIVATE_KEY
        value: |
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
