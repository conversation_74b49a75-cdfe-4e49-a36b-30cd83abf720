// UI management module

class UIManager {
    constructor() {
        this.currentView = 'dashboard';
        this.currentOrder = null;
        this.orders = [];
        this.filteredOrders = [];
        this.isEditing = false;
    }

    // Loading spinner management
    showLoading() {
        document.getElementById('loading-spinner').style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loading-spinner').style.display = 'none';
    }

    // Toast notifications
    showToast(message, type = 'info', duration = CONFIG.APP.TOAST_DURATION) {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas ${this.getToastIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        const container = document.getElementById('toast-container');
        container.appendChild(toast);

        // Auto remove after duration
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, duration);

        // Click to dismiss
        toast.addEventListener('click', () => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        });
    }

    getToastIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    // Error message display
    showError(elementId, message) {
        const errorElement = document.getElementById(elementId);
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.classList.add('show');
        }
    }

    hideError(elementId) {
        const errorElement = document.getElementById(elementId);
        if (errorElement) {
            errorElement.classList.remove('show');
        }
    }

    // View management
    showView(viewName) {
        // Hide all views
        document.querySelectorAll('.view').forEach(view => {
            view.classList.remove('active');
        });

        // Show target view
        const targetView = document.getElementById(`${viewName}-view`);
        if (targetView) {
            targetView.classList.add('active');
            this.currentView = viewName;
        }
    }

    // Dashboard methods
    updateStats(stats) {
        document.getElementById('total-orders').textContent = stats.total || 0;
        document.getElementById('pending-orders').textContent = 
            (stats.byStatus?.New || 0) + (stats.byStatus?.Processing || 0);
        document.getElementById('shipped-orders').textContent = stats.byStatus?.Shipped || 0;
        document.getElementById('delivered-orders').textContent = stats.byStatus?.Delivered || 0;
    }

    // Orders table management
    renderOrdersTable(orders) {
        this.orders = orders;
        this.filteredOrders = [...orders];
        
        const tbody = document.getElementById('orders-tbody');
        tbody.innerHTML = '';

        if (orders.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" style="text-align: center; padding: 2rem; color: #6b7280;">
                        No orders found
                    </td>
                </tr>
            `;
            return;
        }

        orders.forEach(order => {
            const row = this.createOrderRow(order);
            tbody.appendChild(row);
        });
    }

    createOrderRow(order) {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><strong>#${order.id.substring(0, 8)}</strong></td>
            <td>${order.customerName}</td>
            <td>${order.customerPhone}</td>
            <td>${order.articleModel}</td>
            <td>${order.quantity}</td>
            <td><span class="status-badge status-${order.status.toLowerCase()}">${order.status}</span></td>
            <td>${this.formatDate(order.createdAt)}</td>
            <td>
                <button class="btn btn-sm" onclick="UI.viewOrder('${order.id}')" title="View Details">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm" onclick="UI.editOrder('${order.id}')" title="Edit">
                    <i class="fas fa-edit"></i>
                </button>
            </td>
        `;
        return row;
    }

    // Order form management
    showOrderForm(order = null) {
        this.isEditing = order !== null;
        this.currentOrder = order;

        const formTitle = document.getElementById('form-title');
        const form = document.getElementById('order-form');

        if (this.isEditing) {
            formTitle.innerHTML = '<i class="fas fa-edit"></i> Edit Order';
            this.populateOrderForm(order);
        } else {
            formTitle.innerHTML = '<i class="fas fa-plus"></i> New Order';
            form.reset();
        }

        this.showView('order-form');
    }

    populateOrderForm(order) {
        document.getElementById('customer-name').value = order.customerName || '';
        document.getElementById('customer-phone').value = order.customerPhone || '';
        document.getElementById('delivery-address').value = order.deliveryAddress || '';
        document.getElementById('article-model').value = order.articleModel || '';
        document.getElementById('quantity').value = order.quantity || '';
        document.getElementById('status').value = order.status || 'New';
        document.getElementById('notes').value = order.notes || '';
    }

    getOrderFormData() {
        return {
            customerName: document.getElementById('customer-name').value.trim(),
            customerPhone: document.getElementById('customer-phone').value.trim(),
            deliveryAddress: document.getElementById('delivery-address').value.trim(),
            articleModel: document.getElementById('article-model').value.trim(),
            quantity: parseInt(document.getElementById('quantity').value) || 0,
            status: document.getElementById('status').value,
            notes: document.getElementById('notes').value.trim()
        };
    }

    // Order detail view
    showOrderDetail(order) {
        this.currentOrder = order;
        
        const detailTitle = document.getElementById('detail-title');
        detailTitle.innerHTML = `<i class="fas fa-file-alt"></i> Order #${order.id.substring(0, 8)}`;

        const detailsContainer = document.getElementById('order-details');
        detailsContainer.innerHTML = `
            <div class="detail-grid">
                <div class="detail-item">
                    <label>Customer Name</label>
                    <div class="value">${order.customerName}</div>
                </div>
                <div class="detail-item">
                    <label>Phone Number</label>
                    <div class="value">${order.customerPhone}</div>
                </div>
                <div class="detail-item">
                    <label>Article Model</label>
                    <div class="value">${order.articleModel}</div>
                </div>
                <div class="detail-item">
                    <label>Quantity</label>
                    <div class="value">${order.quantity}</div>
                </div>
                <div class="detail-item">
                    <label>Status</label>
                    <div class="value">
                        <span class="status-badge status-${order.status.toLowerCase()}">${order.status}</span>
                    </div>
                </div>
                <div class="detail-item">
                    <label>Created Date</label>
                    <div class="value">${this.formatDate(order.createdAt)}</div>
                </div>
                <div class="detail-item" style="grid-column: 1 / -1;">
                    <label>Delivery Address</label>
                    <div class="value">${order.deliveryAddress}</div>
                </div>
                ${order.notes ? `
                <div class="detail-item" style="grid-column: 1 / -1;">
                    <label>Notes</label>
                    <div class="value">${order.notes}</div>
                </div>
                ` : ''}
            </div>
        `;

        this.showView('order-detail');
    }

    // Search and filter
    filterOrders() {
        const searchTerm = document.getElementById('search-orders').value.toLowerCase();
        const statusFilter = document.getElementById('filter-status').value;

        this.filteredOrders = this.orders.filter(order => {
            const matchesSearch = !searchTerm || 
                order.customerName.toLowerCase().includes(searchTerm) ||
                order.customerPhone.includes(searchTerm) ||
                order.articleModel.toLowerCase().includes(searchTerm) ||
                order.deliveryAddress.toLowerCase().includes(searchTerm);

            const matchesStatus = !statusFilter || order.status === statusFilter;

            return matchesSearch && matchesStatus;
        });

        this.renderOrdersTable(this.filteredOrders);
    }

    // Utility methods
    formatDate(date) {
        if (!date) return 'N/A';
        
        const d = date instanceof Date ? date : new Date(date);
        return d.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // Action methods (called from HTML)
    async viewOrder(orderId) {
        try {
            this.showLoading();
            const response = await API.getOrder(orderId);
            if (response.success) {
                this.showOrderDetail(response.data);
            } else {
                this.showToast('Failed to load order details', 'error');
            }
        } catch (error) {
            console.error('Error viewing order:', error);
            this.showToast('Error loading order details', 'error');
        } finally {
            this.hideLoading();
        }
    }

    async editOrder(orderId) {
        try {
            this.showLoading();
            const response = await API.getOrder(orderId);
            if (response.success) {
                this.showOrderForm(response.data);
            } else {
                this.showToast('Failed to load order for editing', 'error');
            }
        } catch (error) {
            console.error('Error loading order for edit:', error);
            this.showToast('Error loading order for editing', 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Form validation
    validateOrderForm(data) {
        const errors = [];

        if (!data.customerName) errors.push('Customer name is required');
        if (!data.customerPhone) errors.push('Phone number is required');
        if (!data.deliveryAddress) errors.push('Delivery address is required');
        if (!data.articleModel) errors.push('Article model is required');
        if (!data.quantity || data.quantity < 1) errors.push('Valid quantity is required');

        return errors;
    }
}

// Create global UI manager instance
window.UI = new UIManager();
