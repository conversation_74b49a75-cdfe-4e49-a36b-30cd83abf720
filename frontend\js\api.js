// API module for backend communication

class APIManager {
    constructor() {
        this.baseURL = CONFIG.API.BASE_URL;
        this.timeout = CONFIG.API.TIMEOUT;
        this.retryAttempts = CONFIG.API.RETRY_ATTEMPTS;
    }

    // Generic API request method
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        console.log('API Request URL:', url);
        console.log('Base URL:', this.baseURL);
        console.log('Endpoint:', endpoint);

        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
            timeout: this.timeout
        };

        // Add authentication token if available
        const token = await Auth.getAuthToken();
        if (token) {
            defaultOptions.headers.Authorization = `Bearer ${token}`;
        }

        const finalOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };

        try {
            const response = await this.fetchWithTimeout(url, finalOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            return data;
        } catch (error) {
            console.error(`API request failed: ${endpoint}`, error);
            throw this.handleAPIError(error);
        }
    }

    // Fetch with timeout support
    async fetchWithTimeout(url, options) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);

        try {
            const response = await fetch(url, {
                ...options,
                signal: controller.signal
            });
            clearTimeout(timeoutId);
            return response;
        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    }

    // Handle API errors
    handleAPIError(error) {
        if (error.name === 'AbortError') {
            return new Error('Request timeout');
        }
        
        if (error.message.includes('401')) {
            // Unauthorized - token might be expired
            Auth.clearStoredAuth();
            return new Error('Authentication required');
        }
        
        if (error.message.includes('403')) {
            return new Error('Access denied');
        }
        
        if (error.message.includes('404')) {
            return new Error('Resource not found');
        }
        
        if (error.message.includes('500')) {
            return new Error('Server error');
        }
        
        return error;
    }

    // Authentication endpoints
    async verifyAuth() {
        return this.request('/auth/verify', { method: 'POST' });
    }

    async refreshAuth() {
        return this.request('/auth/refresh', { method: 'POST' });
    }

    async getUserProfile() {
        return this.request('/auth/profile');
    }

    // Order endpoints
    async getOrders(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const endpoint = queryString ? `/orders?${queryString}` : '/orders';
        return this.request(endpoint);
    }

    async getOrder(orderId) {
        return this.request(`/orders/${orderId}`);
    }

    async createOrder(orderData) {
        return this.request('/orders', {
            method: 'POST',
            body: JSON.stringify(orderData)
        });
    }

    async updateOrder(orderId, updateData) {
        return this.request(`/orders/${orderId}`, {
            method: 'PUT',
            body: JSON.stringify(updateData)
        });
    }

    async deleteOrder(orderId) {
        return this.request(`/orders/${orderId}`, {
            method: 'DELETE'
        });
    }

    // Utility methods
    async healthCheck() {
        try {
            const response = await fetch(`${this.baseURL.replace('/api', '')}/health`);
            return response.ok;
        } catch (error) {
            return false;
        }
    }

    // Retry mechanism for failed requests
    async requestWithRetry(endpoint, options = {}, attempts = this.retryAttempts) {
        try {
            return await this.request(endpoint, options);
        } catch (error) {
            if (attempts > 1 && !error.message.includes('401') && !error.message.includes('403')) {
                console.log(`Retrying request to ${endpoint}, attempts remaining: ${attempts - 1}`);
                await this.delay(1000); // Wait 1 second before retry
                return this.requestWithRetry(endpoint, options, attempts - 1);
            }
            throw error;
        }
    }

    // Utility delay function
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Batch operations
    async batchUpdateOrders(updates) {
        const promises = updates.map(update => 
            this.updateOrder(update.id, update.data)
        );
        
        try {
            const results = await Promise.allSettled(promises);
            const successful = results.filter(r => r.status === 'fulfilled').length;
            const failed = results.filter(r => r.status === 'rejected').length;
            
            return {
                successful,
                failed,
                total: updates.length,
                results
            };
        } catch (error) {
            console.error('Batch update failed:', error);
            throw error;
        }
    }

    // Search orders
    async searchOrders(query, filters = {}) {
        const params = {
            search: query,
            ...filters
        };
        return this.getOrders(params);
    }

    // Get order statistics
    async getOrderStats() {
        return this.request('/orders/stats');
    }
}

// Create global API manager instance
window.API = new APIManager();
