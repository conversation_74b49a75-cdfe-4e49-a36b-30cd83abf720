const admin = require('../config/firebase');

/**
 * Middleware to authenticate Firebase ID tokens
 */
const authenticateToken = async (req, res, next) => {
  try {
    // Check if Firebase is properly configured
    if (!process.env.FIREBASE_PROJECT_ID || process.env.FIREBASE_PROJECT_ID.includes('placeholder')) {
      return res.status(503).json({
        success: false,
        error: 'Firebase authentication not configured. Please check server setup.'
      });
    }

    // Get token from Authorization header
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'No token provided or invalid format'
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify the token with Firebase Admin
    const decodedToken = await admin.auth().verifyIdToken(token);

    // Add user info to request object
    req.user = {
      uid: decodedToken.uid,
      email: decodedToken.email,
      name: decodedToken.name,
      email_verified: decodedToken.email_verified
    };

    next();
  } catch (error) {
    console.error('Token verification failed:', error);

    let errorMessage = 'Invalid token';
    let statusCode = 401;

    if (error.message.includes('Firebase not configured')) {
      errorMessage = 'Firebase authentication not configured';
      statusCode = 503;
    } else if (error.code === 'auth/id-token-expired') {
      errorMessage = 'Token expired';
    } else if (error.code === 'auth/id-token-revoked') {
      errorMessage = 'Token revoked';
    } else if (error.code === 'auth/invalid-id-token') {
      errorMessage = 'Invalid token format';
    }

    return res.status(statusCode).json({
      success: false,
      error: errorMessage
    });
  }
};

/**
 * Optional authentication middleware - doesn't fail if no token
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      const decodedToken = await admin.auth().verifyIdToken(token);
      
      req.user = {
        uid: decodedToken.uid,
        email: decodedToken.email,
        name: decodedToken.name,
        email_verified: decodedToken.email_verified
      };
    }
    
    next();
  } catch (error) {
    // Continue without authentication if token is invalid
    console.warn('Optional auth failed:', error.message);
    next();
  }
};

module.exports = {
  authenticateToken,
  optionalAuth
};
