# Firebase Service Account Setup Guide

## What's the Difference?

You have **two different** Firebase configurations:

### 1. Web App Config (Already Done ✅)
This is for the **frontend** and is already configured in `frontend/js/config.js`:
```javascript
{
  apiKey: "AIzaSyB0qZ0y0ZZKcv71SOa3RMUuEmvmxBec5tA",
  authDomain: "lavive-cb219.firebaseapp.com",
  projectId: "lavive-cb219",
  // ... etc
}
```

### 2. Service Account Config (Still Needed ❌)
This is for the **backend server** and needs to be added to `backend/.env`

## How to Get Service Account Credentials

### Step 1: Go to Firebase Console
1. Open [Firebase Console](https://console.firebase.google.com/)
2. Select your project: **lavive-cb219**

### Step 2: Generate Service Account Key
1. Click the **Settings gear icon** ⚙️
2. Click **Project settings**
3. Go to **Service accounts** tab
4. Click **Generate new private key** button
5. Click **Generate key** in the popup
6. A JSON file will download (keep it safe!)

### Step 3: Extract Values from JSON
The downloaded JSON file will look like this:
```json
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
```

### Step 4: Update backend/.env
Copy the values from the JSON file to your `backend/.env` file:

```env
FIREBASE_PROJECT_ID=lavive-cb219
FIREBASE_PRIVATE_KEY_ID=abc123def456...
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=123456789012345678901
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs/firebase-adminsdk-xyz%40lavive-cb219.iam.gserviceaccount.com
```

**Important Notes:**
- Keep the quotes around `FIREBASE_PRIVATE_KEY`
- Don't remove the `\n` characters in the private key
- The `client_email` will start with `firebase-adminsdk-`

### Step 5: Enable Required Services
1. **Authentication**: Go to Authentication → Get started → Sign-in method → Enable Email/Password
2. **Firestore**: Go to Firestore Database → Create database → Start in test mode

### Step 6: Test
After updating the `.env` file, restart your backend server:
```bash
cd backend
npm run dev
```

You should see:
```
✅ Firebase Admin SDK initialized successfully
🚀 Commed Delivery API server running on port 3000
```

## Security Warning ⚠️
- **Never commit** the service account JSON file to Git
- **Never share** the private key publicly
- The `.env` file is already in `.gitignore` to protect your credentials

## Troubleshooting

### "Invalid PEM formatted message"
- You're using web app config instead of service account config
- Make sure you downloaded the **Service Account** JSON, not web app config

### "Service account object must contain a string 'project_id' property"
- The `FIREBASE_PROJECT_ID` is missing or incorrect
- Make sure it matches your Firebase project ID: `lavive-cb219`

### "Permission denied"
- Make sure Firestore is created and in "test mode"
- Check that Authentication is enabled
