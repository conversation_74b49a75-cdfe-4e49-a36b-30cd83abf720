# Deployment Guide

## Overview

This guide covers deploying the Commed Delivery Management System to production environments.

## Recommended Architecture

- **Frontend**: Firebase Hosting (free tier available)
- **Backend**: Railway, Render, or Heroku (free tiers available)
- **Database**: Firebase Firestore (free tier available)
- **Authentication**: Firebase Auth (free tier available)

## Pre-deployment Checklist

### Security
- [ ] Configure Firestore security rules
- [ ] Set up proper environment variables
- [ ] Enable HTTPS for all endpoints
- [ ] Configure CORS properly
- [ ] Set up rate limiting
- [ ] Review authentication settings

### Performance
- [ ] Optimize frontend assets
- [ ] Configure caching headers
- [ ] Set up CDN if needed
- [ ] Test with production data volume

### Monitoring
- [ ] Set up error logging
- [ ] Configure performance monitoring
- [ ] Set up uptime monitoring
- [ ] Configure backup strategy

## Frontend Deployment (Firebase Hosting)

### 1. Install Firebase CLI

```bash
npm install -g firebase-tools
```

### 2. Login to Firebase

```bash
firebase login
```

### 3. Initialize Firebase Hosting

```bash
cd frontend
firebase init hosting
```

Choose:
- Use existing project (select your Firebase project)
- Public directory: `.` (current directory)
- Single-page app: `Yes`
- Overwrite index.html: `No`

### 4. Update Configuration

Update `js/config.js` with production URLs:

```javascript
const API_CONFIG = {
    BASE_URL: 'https://your-backend-url.com/api',
    // ... other config
};
```

### 5. Deploy

```bash
firebase deploy --only hosting
```

Your frontend will be available at: `https://your-project-id.web.app`

## Backend Deployment

### Option 1: Railway

#### 1. Create Railway Account
Go to [Railway](https://railway.app) and sign up.

#### 2. Create New Project
- Click "New Project"
- Choose "Deploy from GitHub repo"
- Connect your repository
- Select the backend folder

#### 3. Configure Environment Variables
In Railway dashboard, go to Variables tab and add:

```
NODE_ENV=production
PORT=3000
FRONTEND_URL=https://your-project-id.web.app
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour-private-key\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=your-service-account-email
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=your-cert-url
```

#### 4. Deploy
Railway will automatically deploy when you push to your repository.

### Option 2: Render

#### 1. Create Render Account
Go to [Render](https://render.com) and sign up.

#### 2. Create Web Service
- Click "New +" → "Web Service"
- Connect your repository
- Configure:
  - Name: `commed-delivery-backend`
  - Environment: `Node`
  - Build Command: `cd backend && npm install`
  - Start Command: `cd backend && npm start`

#### 3. Add Environment Variables
Same as Railway configuration above.

### Option 3: Heroku

#### 1. Install Heroku CLI
Download from [Heroku CLI](https://devcenter.heroku.com/articles/heroku-cli)

#### 2. Create Heroku App

```bash
cd backend
heroku create commed-delivery-backend
```

#### 3. Set Environment Variables

```bash
heroku config:set NODE_ENV=production
heroku config:set FRONTEND_URL=https://your-project-id.web.app
heroku config:set FIREBASE_PROJECT_ID=your-project-id
# ... add all other Firebase config variables
```

#### 4. Deploy

```bash
git add .
git commit -m "Deploy to Heroku"
git push heroku main
```

## Database Security (Firestore Rules)

### 1. Configure Security Rules

In Firebase Console, go to Firestore Database → Rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Orders collection - authenticated users only
    match /orders/{orderId} {
      allow read, write: if request.auth != null && request.auth.token.email_verified == true;
    }
    
    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
```

### 2. Test Security Rules

Use the Firebase Console Rules Playground to test your rules.

## Domain Configuration (Optional)

### 1. Custom Domain for Frontend

In Firebase Console:
1. Go to Hosting
2. Click "Add custom domain"
3. Follow the verification steps
4. Update DNS records as instructed

### 2. Custom Domain for Backend

Most hosting providers offer custom domain configuration in their dashboards.

## SSL/HTTPS Configuration

- Firebase Hosting: Automatic HTTPS
- Railway: Automatic HTTPS
- Render: Automatic HTTPS
- Heroku: Automatic HTTPS

## Monitoring and Logging

### 1. Frontend Monitoring

Add to `js/config.js`:

```javascript
// Google Analytics (optional)
const GA_TRACKING_ID = 'GA_TRACKING_ID';

// Error tracking (optional - use Sentry, LogRocket, etc.)
```

### 2. Backend Monitoring

Add to `server.js`:

```javascript
// Production logging
if (process.env.NODE_ENV === 'production') {
  // Add production logging service
  // Examples: Winston, Loggly, Papertrail
}
```

## Backup Strategy

### 1. Firestore Backup

Set up automated Firestore exports:

```bash
# Using gcloud CLI
gcloud firestore export gs://your-backup-bucket
```

### 2. Code Backup

- Use Git with remote repositories (GitHub, GitLab, etc.)
- Tag releases for easy rollback
- Keep deployment scripts in version control

## Performance Optimization

### 1. Frontend Optimization

- Minify CSS and JavaScript
- Optimize images
- Enable gzip compression
- Use CDN for static assets

### 2. Backend Optimization

- Enable compression middleware
- Implement caching where appropriate
- Optimize database queries
- Use connection pooling

## Health Checks and Uptime Monitoring

### 1. Health Check Endpoint

Already implemented at `/health`

### 2. Uptime Monitoring Services

- UptimeRobot (free)
- Pingdom
- StatusCake
- New Relic

## Rollback Strategy

### 1. Frontend Rollback

```bash
# Firebase Hosting keeps previous versions
firebase hosting:clone SOURCE_SITE_ID:SOURCE_VERSION_ID TARGET_SITE_ID
```

### 2. Backend Rollback

- Railway: Use deployment history
- Render: Use deployment history  
- Heroku: `heroku rollback`

## Post-Deployment Testing

### 1. Functional Testing

- [ ] User authentication works
- [ ] Order creation works
- [ ] Order listing works
- [ ] Order updates work
- [ ] Search and filtering work
- [ ] Mobile responsiveness

### 2. Performance Testing

- [ ] Page load times < 3 seconds
- [ ] API response times < 1 second
- [ ] Database queries optimized

### 3. Security Testing

- [ ] HTTPS enforced
- [ ] Authentication required for protected routes
- [ ] Input validation working
- [ ] Rate limiting active

## Maintenance

### 1. Regular Updates

- Keep dependencies updated
- Monitor security advisories
- Update Firebase SDK versions
- Review and update security rules

### 2. Monitoring

- Check error logs regularly
- Monitor performance metrics
- Review user feedback
- Monitor costs and usage

## Troubleshooting Common Issues

### CORS Errors
- Check `FRONTEND_URL` environment variable
- Verify CORS configuration in backend

### Authentication Issues
- Verify Firebase configuration
- Check environment variables
- Ensure Firestore rules are correct

### Performance Issues
- Check database query efficiency
- Monitor API response times
- Review frontend bundle size

### Deployment Failures
- Check build logs
- Verify environment variables
- Ensure all dependencies are listed in package.json
